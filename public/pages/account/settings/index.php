<?php
// Sample user data - in a real app, this would come from a database
$user = [
    'name' => 'Mustapha',
    'email' => '<EMAIL>',
    'avatar' => IMAGE_SRC . 'profile.png',
    'joinDate' => '2024-01-15'
];

// Sample subscription data
$subscriptions = [
    [
        'id' => 1,
        'plan' => 'Basic Plan',
        'price' => '$9.99',
        'period' => 'month',
        'status' => 'active',
        'expiresOn' => '2025-12-01',
        'autoRenew' => true
    ],
    [
        'id' => 2,
        'plan' => 'Standard Plan',
        'price' => '$14.99',
        'period' => 'month',
        'status' => 'active',
        'expiresOn' => '2025-12-01',
        'autoRenew' => true
    ],
    [
        'id' => 3,
        'plan' => 'Premium Plan',
        'price' => '$19.99',
        'period' => 'month',
        'status' => 'expired',
        'expiresOn' => '2024-11-01',
        'autoRenew' => false
    ]
];

?>

<div class="w-full pt-28 pb-14 lp-sm:px-20 tb-lg:px-10 px-4">
    <div class="flex flex-col justify-start items-start gap-10">
        <?php include __DIR__ . "/../hero.php"; ?>
        <!-- Settings Section -->
        <div
            class="w-full p-12 bg-stone-950 rounded-xl outline outline-1 outline-offset-[-1px] outline-neutral-800 inline-flex justify-center items-start gap-12">
            <!-- Sidebar Navigation -->
            <div class="w-72 inline-flex flex-col justify-start items-start gap-7">
                <button type="button" onclick="showSection('profile')"
                    class="self-stretch px-6 py-3.5 bg-red-600 rounded-[10px] outline outline-2 outline-neutral-800 inline-flex justify-start items-center gap-2.5 hover:bg-red-500 transition-colors duration-300 settings-nav-btn active"
                    data-section="profile">
                    <div class="justify-start text-white text-lg font-medium font-['Manrope'] leading-relaxed">Profile
                        Details</div>
                </button>
                <button type="button" onclick="showSection('password')"
                    class="self-stretch px-6 py-3.5 bg-stone-900 rounded-[10px] outline outline-2 outline-neutral-800 inline-flex justify-start items-center gap-2.5 hover:bg-stone-800 hover:outline-neutral-700 transition-all duration-300 settings-nav-btn"
                    data-section="password">
                    <div class="justify-start text-white text-lg font-medium font-['Manrope'] leading-relaxed">Change
                        Password</div>
                </button>
                <button type="button" onclick="showSection('support')"
                    class="self-stretch px-6 py-3.5 bg-stone-900 rounded-[10px] outline outline-2 outline-neutral-800 inline-flex justify-start items-center gap-2.5 hover:bg-stone-800 hover:outline-neutral-700 transition-all duration-300 settings-nav-btn"
                    data-section="support">
                    <div class="justify-start text-white text-lg font-medium font-['Manrope'] leading-relaxed">Support
                        Tickets</div>
                </button>
            </div>
            <!-- Content Sections -->
            <div class="flex-1">
                <!-- Profile Section -->
                <div id="profile-section" class="settings-section">
                    <form action="/account/settings/update" method="POST"
                        class="flex-1 inline-flex flex-col justify-start items-start gap-12 w-full">
                        <div
                            class="self-stretch flex flex-col justify-start items-start gap-5 border-b custom-border-image">
                            <div
                                class="self-stretch justify-start text-white text-2xl font-bold font-['Manrope'] leading-9">
                                Edit Profile</div>
                        </div>

                        <!-- Full Name Field -->
                        <div class="self-stretch inline-flex justify-start items-start gap-12">
                            <div class="flex-1 flex flex-col justify-start items-start gap-4">
                                <label for="full-name"
                                    class="text-white mb-md:text-lg text-base font-semibold leading-relaxed font-manrope">
                                    Full Name
                                </label>
                                <div
                                    class="self-stretch mb-md:p-5 p-4 bg-neutral-900 rounded-lg outline outline-1 outline-offset-[-1px] outline-neutral-800 flex justify-start items-center gap-20 overflow-hidden">
                                    <input type="text" name="full_name" id="full-name"
                                        value="<?= htmlspecialchars($user['name']) ?>"
                                        placeholder="Enter your full name"
                                        class="flex-1 text-neutral-400 font-normal leading-relaxed font-manrope bg-transparent border-none outline-none mb-md:text-lg text-sm"
                                        required />
                                </div>
                            </div>
                        </div>

                        <!-- Email Address Field -->
                        <div class="self-stretch inline-flex justify-start items-start gap-12">
                            <div class="flex-1 flex flex-col justify-start items-start gap-4">
                                <label for="email-address"
                                    class="text-white mb-md:text-lg text-base font-semibold leading-relaxed font-manrope">
                                    Email Address
                                </label>
                                <div
                                    class="self-stretch mb-md:p-5 p-4 bg-neutral-900 rounded-lg outline outline-1 outline-offset-[-1px] outline-neutral-800 flex justify-start items-center gap-20 overflow-hidden">
                                    <input type="email" name="email" id="email-address"
                                        value="<?= htmlspecialchars($user['email']) ?>"
                                        placeholder="Enter your email address"
                                        class="flex-1 text-neutral-400 font-normal leading-relaxed font-manrope bg-transparent border-none outline-none mb-md:text-lg text-sm"
                                        required />
                                </div>
                            </div>
                        </div>

                        <!-- Phone Number Field -->
                        <div class="self-stretch inline-flex justify-start items-start gap-12">
                            <div class="flex-1 flex flex-col justify-start items-start gap-4">
                                <label for="phone-number"
                                    class="text-white mb-md:text-lg text-base font-semibold leading-relaxed font-manrope">
                                    Phone Number
                                </label>
                                <div class="self-stretch flex justify-start items-stretch mb-md:gap-4 gap-3">
                                    <?php renderCountryCode(); ?>
                                    <div class="flex-1">
                                        <div
                                            class="self-stretch mb-md:p-5 p-4 bg-neutral-900 rounded-r-lg outline outline-1 outline-offset-[-1px] outline-neutral-800 flex justify-start items-center gap-20 overflow-hidden">
                                            <input type="tel" name="phone" id="phone-number"
                                                placeholder="Enter phone number"
                                                class="flex-1 text-neutral-400 font-normal leading-relaxed font-manrope bg-transparent border-none outline-none mb-md:text-lg text-sm" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Form Action Buttons -->
                        <div class="self-stretch inline-flex justify-end items-center gap-5">
                            <?php renderCancelButton("Cancel", "/account/settings"); ?>
                            <?php renderSaveButton("Save Changes"); ?>
                        </div>
                    </form>
                </div>

                <!-- Password Change Section -->
                <div id="password-section" class="settings-section hidden">
                    <form action="/account/settings/change-password" method="POST"
                        class="flex-1 inline-flex flex-col justify-start items-start gap-12 w-full">
                        <div
                            class="self-stretch flex flex-col justify-start items-start gap-5 border-b custom-border-image">
                            <div
                                class="self-stretch justify-start text-white text-2xl font-bold font-['Manrope'] leading-9">
                                Change Password</div>
                        </div>

                        <!-- Current Password Field -->
                        <div class="self-stretch inline-flex justify-start items-start gap-12">
                            <div class="flex-1 flex flex-col justify-start items-start gap-4">
                                <label for="current-password"
                                    class="text-white mb-md:text-lg text-base font-semibold leading-relaxed font-manrope">
                                    Current Password
                                </label>
                                <div
                                    class="self-stretch mb-md:p-5 p-4 bg-neutral-900 rounded-lg outline outline-1 outline-offset-[-1px] outline-neutral-800 flex justify-between items-center gap-4 overflow-hidden">
                                    <input type="password" name="current_password" id="current-password"
                                        placeholder="Enter your current password"
                                        class="flex-1 text-neutral-400 font-normal leading-relaxed font-manrope bg-transparent border-none outline-none mb-md:text-lg text-sm"
                                        required />
                                    <button type="button" class="password-toggle" data-target="current-password">
                                        <?php icon('mdi:eye-outline', 'text-neutral-400 text-xl hover:text-white transition-colors duration-200'); ?>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- New Password Field -->
                        <div class="self-stretch inline-flex justify-start items-start gap-12">
                            <div class="flex-1 flex flex-col justify-start items-start gap-4">
                                <label for="new-password"
                                    class="text-white mb-md:text-lg text-base font-semibold leading-relaxed font-manrope">
                                    New Password
                                </label>
                                <div
                                    class="self-stretch mb-md:p-5 p-4 bg-neutral-900 rounded-lg outline outline-1 outline-offset-[-1px] outline-neutral-800 flex justify-between items-center gap-4 overflow-hidden">
                                    <input type="password" name="new_password" id="new-password"
                                        placeholder="Enter your new password"
                                        class="flex-1 text-neutral-400 font-normal leading-relaxed font-manrope bg-transparent border-none outline-none mb-md:text-lg text-sm"
                                        required />
                                    <button type="button" class="password-toggle" data-target="new-password">
                                        <?php icon('mdi:eye-outline', 'text-neutral-400 text-xl hover:text-white transition-colors duration-200'); ?>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Confirm New Password Field -->
                        <div class="self-stretch inline-flex justify-start items-start gap-12">
                            <div class="flex-1 flex flex-col justify-start items-start gap-4">
                                <label for="confirm-password"
                                    class="text-white mb-md:text-lg text-base font-semibold leading-relaxed font-manrope">
                                    Confirm New Password
                                </label>
                                <div
                                    class="self-stretch mb-md:p-5 p-4 bg-neutral-900 rounded-lg outline outline-1 outline-offset-[-1px] outline-neutral-800 flex justify-between items-center gap-4 overflow-hidden">
                                    <input type="password" name="confirm_password" id="confirm-password"
                                        placeholder="Confirm your new password"
                                        class="flex-1 text-neutral-400 font-normal leading-relaxed font-manrope bg-transparent border-none outline-none mb-md:text-lg text-sm"
                                        required />
                                    <button type="button" class="password-toggle" data-target="confirm-password">
                                        <?php icon('mdi:eye-outline', 'text-neutral-400 text-xl hover:text-white transition-colors duration-200'); ?>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Password Requirements -->
                        <div class="self-stretch flex flex-col justify-start items-start gap-3">
                            <div class="text-white text-base font-semibold font-['Manrope'] leading-relaxed">
                                Password Requirements:
                            </div>
                            <ul
                                class="text-neutral-400 text-sm font-normal font-['Manrope'] leading-relaxed space-y-1 ml-4">
                                <li class="flex items-center gap-2 password-requirement">
                                    <?php icon('mdi:check-circle-outline', 'text-neutral-500 text-sm'); ?>
                                    At least 8 characters long
                                </li>
                                <li class="flex items-center gap-2 password-requirement">
                                    <?php icon('mdi:check-circle-outline', 'text-neutral-500 text-sm'); ?>
                                    Contains at least one uppercase letter
                                </li>
                                <li class="flex items-center gap-2 password-requirement">
                                    <?php icon('mdi:check-circle-outline', 'text-neutral-500 text-sm'); ?>
                                    Contains at least one lowercase letter
                                </li>
                                <li class="flex items-center gap-2 password-requirement">
                                    <?php icon('mdi:check-circle-outline', 'text-neutral-500 text-sm'); ?>
                                    Contains at least one number
                                </li>
                            </ul>
                        </div>

                        <!-- Form Action Buttons -->
                        <div class="self-stretch inline-flex justify-end items-center gap-5">
                            <?php renderCancelButton("Cancel", "/account/settings"); ?>
                            <?php renderSaveButton("Update"); ?>
                        </div>
                    </form>
                </div>

                <!-- Support Tickets Section -->
                <div id="support-section" class="settings-section hidden">
                    <div class="flex-1 inline-flex flex-col justify-start items-start gap-12 w-full">
                        <div
                            class="self-stretch flex flex-col justify-start items-start gap-5 border-b custom-border-image">
                            <div
                                class="self-stretch justify-start text-white text-2xl font-bold font-['Manrope'] leading-9">
                                Support Tickets</div>
                        </div>

                        <div class="self-stretch flex flex-col justify-center items-center gap-8 py-16">
                            <?php icon('mdi:ticket-outline', 'text-neutral-600 text-6xl'); ?>
                            <div class="text-center">
                                <div class="text-white text-xl font-semibold font-['Manrope'] leading-relaxed mb-2">
                                    No Support Tickets
                                </div>
                                <div
                                    class="text-neutral-400 text-base font-normal font-['Manrope'] leading-relaxed mb-6">
                                    You haven't created any support tickets yet.
                                </div>
                                <?php renderSubmitButton("Create New Ticket", "bg-red-600 hover:bg-red-500"); ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for Section Switching -->
<script>
    function showSection(sectionName) {
        // Hide all sections
        document.querySelectorAll('.settings-section').forEach(section => {
            section.classList.add('hidden');
        });

        // Show selected section
        document.getElementById(sectionName + '-section').classList.remove('hidden');

        // Update navigation buttons
        document.querySelectorAll('.settings-nav-btn').forEach(btn => {
            btn.classList.remove('active', 'bg-red-600');
            btn.classList.add('bg-stone-900');
        });

        // Activate selected button
        const activeBtn = document.querySelector(`[data-section="${sectionName}"]`);
        activeBtn.classList.add('active', 'bg-red-600');
        activeBtn.classList.remove('bg-stone-900');
    }
</script>