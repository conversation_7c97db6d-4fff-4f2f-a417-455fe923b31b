function showSection(sectionName: string): void {
    // Hide all sections
    document.querySelectorAll('.settings-section').forEach((section: Element) => {
        section.classList.add('hidden');
    });

    // Show selected section
    const selectedSection = document.getElementById(sectionName + '-section');
    if (selectedSection) selectedSection.classList.remove('hidden');

    // Update navigation buttons
    document.querySelectorAll('.settings-nav-btn').forEach((btn: Element) => {
        btn.classList.remove('active', 'bg-red-600', 'outline-red-500');
        btn.classList.add('bg-stone-900', 'outline-neutral-800');
    });

    // Activate selected button
    const activeBtn = document.querySelector(`[data-section="${sectionName}"]`) as HTMLElement;
    if (activeBtn) {
        activeBtn.classList.add('active', 'bg-red-600', 'outline-red-500');
        activeBtn.classList.remove('bg-stone-900', 'outline-neutral-800');
    }
}
